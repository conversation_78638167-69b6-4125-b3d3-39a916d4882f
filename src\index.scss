@use "@/variables" as *;

@font-face {
  font-family: "Manrope";
  src: url("/src/assets/fonts/Manrope-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "Manrope";
  src: url("/src/assets/fonts/Manrope-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Manrope";
  src: url("/src/assets/fonts/Manrope-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Manrope";
  src: url("/src/assets/fonts/Manrope-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: "Manrope";
  src: url("/src/assets/fonts/Manrope-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: "Manrope";
  src: url("/src/assets/fonts/Manrope-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
}

body {
  font-family: "Manrope", sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: $black-color;
}

p {
  color: $text-color;
}

a {
  text-decoration: none;
  transition: 0.3s ease-in-out;
}

.inner-height {
  min-height: calc(100dvh - 100px);
}

.container-fluid {
  padding: 0px 80px;
}

.members-list {
  padding: 24px 0px;
  position: relative;
  width: 100%;

  &-content {
    gap: 20px;
  }
}

.btn {
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
  border-radius: 8px;
  padding: 16px 30px;
  border: 0;
  min-width: 200px;
  background: linear-gradient(270deg, $primary-color 0%, $secondary-color 100%);
  transition: 0.3s ease-in-out;

  &:hover {
    background: linear-gradient(
      270deg,
      $secondary-color 0%,
      $primary-color 100%
    );
  }
}

.bordered-btn {
  background: transparent;
  color: $black-color;
  border: 1px solid;

  &:hover {
    background: $black-color;
    border-color: $black-color;
  }
}

.form-input-group {
  gap: 20px;

  .form-input {
    position: relative;

    svg {
      position: absolute;
      right: 30px;
      bottom: 20px;
      width: 22px;
      height: 22px;
    }

    .form-label {
      color: $black-color;
      font-weight: 500;
    }

    .form-control {
      color: $text-color;
      padding: 18px 30px;
      border-radius: 8px;
      border: 1px solid $light-gray-color;
      background: $white-color;
      box-shadow: none;

      &::placeholder {
        color: $text-color;
      }

      &:-webkit-autofill,
      &:-webkit-autofill:focus {
        transition:
          background-color 600000s 0s,
          color 600000s 0s;
      }

      &[data-autocompleted] {
        background-color: transparent !important;
      }
    }

    .select {
      &__control {
        color: $text-color;
        padding: 16px 30px;
        border-radius: 8px;
        border: 1px solid $light-gray-color;
        background: $white-color;
        box-shadow: none;
      }

      &__indicator-separator {
        display: none !important;
      }

      &__dropdown-indicator {
        padding: 0;

        svg {
          position: unset;
        }
      }

      &__value-container {
        padding: 0;
      }

      &__placeholder,
      &__single-value,
      &__input-container {
        margin: 0;
        color: $text-color;
      }

      &__menu {
        font-size: 15px;
      }

      &__option {
        padding: 15px 30px;

        &.select__option--is-focused {
          background-color: $light-gray-color;
        }

        &.select__option--is-selected {
          background-color: $primary-color;
        }
      }
    }
  }
}

.form-check {
  padding-left: 26px;

  &-input {
    border-color: $text-color;
    border-radius: 0 !important;
    width: 18px;
    height: 18px;
    margin-left: -26px !important;
    margin-top: 4px;

    &:focus {
      box-shadow: none;
      border-color: $text-color;
    }

    &:checked {
      background-color: $primary-color;
      border-color: $primary-color !important;
    }

    &#custom-switch {
      height: 24px;
      width: 40px;
      border-radius: 12px !important;
      border-color: $link-color;
    }
  }

  &-label {
    color: $text-color !important;
  }

  &-input[type="radio"] {
    border-radius: 50% !important;
    border-color: $black-color;

    &.is-valid {
      &:focus {
        box-shadow: none;
      }

      &:checked {
        background-color: $primary-color;
        border-color: $primary-color;
      }
    }
  }
}

::-webkit-scrollbar {
  width: 3px;
  height: 3px;
  //   background-color: $light-gray-color;
}

::-webkit-scrollbar-thumb {
  background-color: $secondary-color;
  border-radius: 3px;
}

@media (max-width: "1399px") {
  .container-fluid {
    padding: 0px 50px;
  }
  .members-list {
    &-content {
      justify-content: center;
    }
  }
}

@media (max-width: "767px") {
  .members-list {
    &-content {
      justify-content: center;
    }
  }

  .btn {
    font-size: 16px;
    min-width: 150px;
  }

  .container-fluid {
    padding: 0px 12px;
  }

  p,
  a {
    font-size: 14px;
  }

  .form-input-group {
    gap: 15px;

    .form-input {
      svg {
        right: 20px;
        bottom: 16px;
        width: 18px;
        height: 18px;
      }

      .form-label {
        font-size: 14px;
      }

      .form-control {
        font-size: 14px;
        padding: 14px 20px;
      }

      .select {
        &__control {
          padding: 12px 20px;
        }

        &__placeholder,
        &__single-value,
        &__input-container {
          font-size: 14px;
        }

        &__menu {
          font-size: 13px;
        }

        &__option {
          padding: 8px 20px;
        }
      }
    }
  }

  .form-check {
    padding-left: 24px;

    &-input {
      width: 16px;
      height: 16px;
      margin-left: -24px !important;
      margin-top: 5px;
    }

    &-label {
      font-size: 14px;
    }
  }
}

.color-primary {
  color: $primary-color !important;
}

.bg-color-primary {
  background-color: $primary-color;
}

.cursor-pointer {
  cursor: pointer;
}

.pac-container {
  z-index: 1051 !important; /* higher than Bootstrap modal (1050) */
}
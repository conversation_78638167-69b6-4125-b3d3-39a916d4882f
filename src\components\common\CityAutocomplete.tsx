import { useMapsLibrary } from "@vis.gl/react-google-maps";
import { useEffect, useRef, useState } from "react";
import { Form } from "react-bootstrap";

interface CityAutocompleteProps {
  onPlaceSelect: (place: google.maps.places.PlaceResult | null) => void;
  countryCode?: string;
  disabled?: boolean;
  placeholder?: string;
  value?: string;
  className?: string;
  isInvalid?: boolean;
}

const CityAutocomplete = ({ 
  onPlaceSelect, 
  countryCode, 
  disabled = false,
  placeholder,
  value,
  className = "form-control",
  isInvalid = false
}: CityAutocompleteProps) => {
  const [placeAutocomplete, setPlaceAutocomplete] = useState<google.maps.places.Autocomplete | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const places = useMapsLibrary("places");

  useEffect(() => {
    if (!places || !inputRef.current) return;

    const options: google.maps.places.AutocompleteOptions = {
      fields: ["geometry", "name", "formatted_address"],
      types: ["(cities)"],
    };

    if (countryCode) {
      options.componentRestrictions = { country: countryCode.toLowerCase() };
    }

    const autocomplete = new places.Autocomplete(inputRef.current, options);
    setPlaceAutocomplete(autocomplete);

    return () => {
      google.maps.event.clearInstanceListeners(autocomplete);
    };
  }, [places, countryCode]);

  useEffect(() => {
    if (!placeAutocomplete) return;

    placeAutocomplete.addListener("place_changed", () => {
      const place = placeAutocomplete.getPlace();
      onPlaceSelect(place.geometry ? place : null); // Only pass valid places with geometry
    });
  }, [placeAutocomplete, onPlaceSelect]);

  // Set the input value when value prop changes
  useEffect(() => {
    if (inputRef.current && value !== undefined) {
      inputRef.current.value = value;
    }
  }, [value]);

  return (
    <Form.Control
      ref={inputRef}
      placeholder={placeholder || (countryCode ? "Select a city" : "Select a country first")}
      disabled={disabled}
      className={className}
      style={{ width: "100%" }}
      isInvalid={isInvalid}
    />
  );
};

export default CityAutocomplete;

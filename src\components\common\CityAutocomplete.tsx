import { useMapsLibrary } from "@vis.gl/react-google-maps";
import { useEffect, useRef, useState } from "react";
import { Form } from "react-bootstrap";

interface CityAutocompleteProps {
  onPlaceSelect: (place: google.maps.places.PlaceResult | null) => void;
  countryCode?: string;
  disabled?: boolean;
  placeholder?: string;
  value?: string;
  className?: string;
  isInvalid?: boolean;
  onBlur?: () => void;
}

const CityAutocomplete = ({
  onPlaceSelect,
  countryCode,
  disabled = false,
  placeholder,
  value,
  className = "form-control",
  isInvalid = false,
  onBlur
}: CityAutocompleteProps) => {
  const [placeAutocomplete, setPlaceAutocomplete] = useState<google.maps.places.Autocomplete | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const places = useMapsLibrary("places");

  useEffect(() => {
    if (!places || !inputRef.current) return;

    const options: google.maps.places.AutocompleteOptions = {
      fields: ["geometry", "name", "formatted_address", "place_id"],
      types: ["(cities)"],
      strictBounds: true, // Enforce strict bounds for better country restriction
    };

    // Ensure country restriction is properly applied
    if (countryCode) {
      options.componentRestrictions = { country: countryCode.toLowerCase() };
    }

    const autocomplete = new places.Autocomplete(inputRef.current, options);

    // Fix dropdown positioning by setting bounds to the container
    if (containerRef.current) {
      const bounds = containerRef.current.getBoundingClientRect();
      autocomplete.setBounds({
        north: bounds.top,
        south: bounds.bottom,
        east: bounds.right,
        west: bounds.left
      });
    }

    setPlaceAutocomplete(autocomplete);

    return () => {
      google.maps.event.clearInstanceListeners(autocomplete);
    };
  }, [places, countryCode]);

  useEffect(() => {
    if (!placeAutocomplete) return;

    const handlePlaceChanged = () => {
      const place = placeAutocomplete.getPlace();
      // Validate that the place has geometry and is within the restricted country
      if (place.geometry && place.place_id) {
        onPlaceSelect(place);
      } else {
        onPlaceSelect(null);
      }
    };

    placeAutocomplete.addListener("place_changed", handlePlaceChanged);

    return () => {
      google.maps.event.clearListeners(placeAutocomplete, "place_changed");
    };
  }, [placeAutocomplete, onPlaceSelect]);

  useEffect(() => {
    if (inputRef.current && value !== undefined) {
      inputRef.current.value = value;
    }
  }, [value]);

  // Add CSS to fix dropdown positioning
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .pac-container {
        position: fixed !important;
        z-index: 9999 !important;
        border-radius: 4px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      }
      .pac-container:after {
        content: none;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div ref={containerRef} style={{ position: "relative" }}>
      <Form.Control
        ref={inputRef}
        placeholder={placeholder || (countryCode ? "Select a city" : "Select a country first")}
        disabled={disabled}
        className={className}
        style={{ width: "100%" }}
        isInvalid={isInvalid}
        onBlur={onBlur}
      />
    </div>
  );
};

export default CityAutocomplete;

import React from "react";
import { Accordion, Form } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import MemoizedSelect from "./MemoizedSelect";

interface LifestyleSectionProps {
  formik: any;
  handleSelectChange: (field: string) => (option: any) => void;
  relationshipStatusOptions: Array<{ value: any; label: string }>;
  starSignOptions: Array<{ value: any; label: string }>;
  smokingHabitOptions: Array<{ value: any; label: string }>;
  drinkingHabitOptions: Array<{ value: any; label: string }>;
  sexualOrientationOptions: Array<{ value: any; label: string }>;
  isMasterLoading: boolean;
}

const LifestyleSection: React.FC<LifestyleSectionProps> = ({
  formik,
  handleSelectChange,
  relationshipStatusOptions,
  // starSignOptions,
  smokingHabitOptions,
  drinkingHabitOptions,
  sexualOrientationOptions,
  isMasterLoading,
}) => {
  const { t } = useTranslation();

  return (
    <Accordion.Item eventKey="2">
      <Accordion.Header>{t("lifestyle")}</Accordion.Header>
      <Accordion.Body>
        <div className="form-input-group d-flex flex-wrap">
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.relationshipStatus")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={relationshipStatusOptions}
              placeholder={isMasterLoading ? t("profileManagement.loading") : t("profileManagement.placeholders.relationshipStatus")}
              classNamePrefix="select"
              name="relationshipStatus"
              value={formik.values.relationshipStatus}
              onChange={handleSelectChange("relationshipStatus")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.smokingHabit")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={smokingHabitOptions}
              placeholder={isMasterLoading ? t("profileManagement.loading") : t("profileManagement.placeholders.smokingHabit")}
              classNamePrefix="select"
              name="smokingHabit"
              value={formik.values.smokingHabit}
              onChange={handleSelectChange("smokingHabit")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.drinkingHabit")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={drinkingHabitOptions}
              placeholder={isMasterLoading ? t("profileManagement.loading") : t("profileManagement.placeholders.drinkingHabit")}
              classNamePrefix="select"
              name="drinkingHabit"
              value={formik.values.drinkingHabit}
              onChange={handleSelectChange("drinkingHabit")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.sexualOrientation")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={sexualOrientationOptions}
              placeholder={isMasterLoading ? t("profileManagement.loading") : t("profileManagement.placeholders.sexualOrientation")}
              classNamePrefix="select"
              name="sexualOrientation"
              value={formik.values.sexualOrientation}
              onChange={handleSelectChange("sexualOrientation")}
            />
          </Form.Group>
        </div>
      </Accordion.Body>
    </Accordion.Item>
  );
};

export default LifestyleSection;
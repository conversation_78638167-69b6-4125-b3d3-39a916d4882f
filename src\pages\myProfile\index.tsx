import { useMyProfile, useUpdateProfileMutation } from "@/api/user.api";
import { useMaster } from "@/api/utils.api";
import AboutMeSection from "@/components/profileManagement/AboutMeSection";
import AppearanceSection from "@/components/profileManagement/AppearanceSection";
import BasicInfoSection from "@/components/profileManagement/BasicInfoSection";
import FamilySection from "@/components/profileManagement/FamilySection";
import LifestyleSection from "@/components/profileManagement/LifestyleSection";
import LocationSection from "@/components/profileManagement/LocationSection";
import MemoSection from "@/components/profileManagement/MemoSection";
import PersonalAttributesSection from "@/components/profileManagement/PersonalAttributesSection";
import ProfilePictureSection from "@/components/profileManagement/ProfilePictureSection";
import { useLogout } from "@/hooks";
import { useProfileAvatar } from "@/hooks/useProfileAvatar";
import { useTranslation } from "@/hooks/useTranslation";
import useUserStore, { setUserInfo } from "@/stores/user";
import { APIProvider } from "@vis.gl/react-google-maps";
import { useFormik } from "formik";
import React, { useEffect, useState } from "react";
import {
  Accordion,
  Button,
  Col,
  Container,
  Form,
  Nav,
  Row,
  Tab,
} from "react-bootstrap";
import toast from "react-hot-toast";
import { useSearchParams } from "react-router-dom";
import defaultProfile from "../../assets/images/user.png";
import ChangePassword from "./ChangePassword";
import MyPhotos from "./MyPhotos";
import Settings from "./Settings";
import "./styles.scss";

const GOOGLE_MAP_API_KEY = import.meta.env.VITE_GOOGLE_MAP_API_KEY;

type tabKeyType = "viewProfile" | "myPhotos" | "changePassword" | "settings";

const MyProfile: React.FC = () => {
  const { t } = useTranslation();
  const { data: myProfile } = useMyProfile();

  const userInfo = useUserStore((state) => state.userInfo);
  const { user: userData } = userInfo;
  const { mutateAsync: updateProfile } = useUpdateProfileMutation();
  const { data: master = {}, isLoading: isMasterLoading } = useMaster();
  const onLogout = useLogout();
  const [searchParams, setSearchParams] = useSearchParams();
  const defaultTabKey: tabKeyType =
    (searchParams.get("tabKey") as tabKeyType) || "viewProfile";
  const [currentKey, setCurrentKey] = useState(defaultTabKey);

  React.useEffect(() => {
    if (myProfile && Object.keys(myProfile).length > 0) {
      setUserInfo({
        ...userInfo,
        user: myProfile,
      });
    }
  }, [myProfile]);

  useEffect(() => {
    if (defaultTabKey) {
      setCurrentKey(defaultTabKey);
    }
  }, [defaultTabKey]);

  const initialAvatar = (userData as any)?.avatar || defaultProfile;
  const { preview, handleImageChange } = useProfileAvatar(initialAvatar);

  const appearanceOptions = React.useMemo(
    () =>
      (master.appearance || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.appearance]
  );
  const relationshipStatusOptions = React.useMemo(
    () =>
      (master.relationship_status || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.relationship_status]
  );
  const personalityOptions = React.useMemo(
    () =>
      (master.personality || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.personality]
  );
  const eyeColorOptions = React.useMemo(
    () =>
      (master.eye_color || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.eye_color]
  );
  const bodyTypeOptions = React.useMemo(
    () =>
      (master.body_type || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.body_type]
  );
  const hairColorOptions = React.useMemo(
    () =>
      (master.hair_color || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.hair_color]
  );
  const smokingHabitOptions = React.useMemo(
    () =>
      (master.smoking_habits || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.smoking_habits]
  );
  const drinkingHabitOptions = React.useMemo(
    () =>
      (master.drinking_habits || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.drinking_habits]
  );
  const bestFeatureOptions = React.useMemo(
    () =>
      (master.best_feature || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.best_feature]
  );
  const bodyArtOptions = React.useMemo(
    () =>
      (master.body_art || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.body_art]
  );
  const sexualOrientationOptions = React.useMemo(
    () =>
      (master.sexual_orientation || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.sexual_orientation]
  );
  const ethnicityOptions = React.useMemo(
    () =>
      (master.ethnicity || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.ethnicity]
  );
  const starSignOptions = React.useMemo(
    () =>
      (master.star_sign || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.star_sign]
  );
  const seekingForOptions = React.useMemo(
    () =>
      (master.seeking_for || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.seeking_for]
  );
  const genderOptions = React.useMemo(
    () =>
      (master.gender || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.gender]
  );
  const religionOptions = React.useMemo(
    () =>
      (master.religion || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.religion]
  );
  const interestsOptions = React.useMemo(
    () =>
      (master.interest || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.interest]
  );

  const profileData = myProfile || userData;

  const initialValues = {
    name: profileData?.name || undefined,
    username: profileData?.username || undefined,
    email: profileData?.email || undefined,
    gender: (profileData as any)?.customer_profile?.gender?.id || undefined,
    seekingFor:
      (profileData as any)?.customer_profile?.seekingFor?.id || undefined,
    dob: (profileData as any)?.customer_profile?.dob || undefined,
    appearance:
      (profileData as any)?.customer_profile?.appearance?.id || undefined,
    hairColor:
      (profileData as any)?.customer_profile?.hairColor?.id || undefined,
    eyeColor: (profileData as any)?.customer_profile?.eyeColor?.id || undefined,
    bodyType: (profileData as any)?.customer_profile?.bodyType?.id || undefined,
    bestFeature:
      (profileData as any)?.customer_profile?.bestFeature?.id || undefined,
    bodyArt: (profileData as any)?.customer_profile?.bodyArt?.id || undefined,
    height: (profileData as any)?.customer_profile?.height || undefined,
    weight: (profileData as any)?.customer_profile?.weight || undefined,
    relationshipStatus:
      (profileData as any)?.customer_profile?.relationshipStatus?.id ||
      undefined,
    starSign: (profileData as any)?.customer_profile?.starSign?.id || undefined,
    smokingHabit:
      (profileData as any)?.customer_profile?.smokingHabit?.id || undefined,
    drinkingHabit:
      (profileData as any)?.customer_profile?.drinkingHabit?.id || undefined,
    sexualOrientation:
      (profileData as any)?.customer_profile?.sexualOrientation?.id ||
      undefined,
    personality:
      (profileData as any)?.customer_profile?.personality?.id || undefined,
    ethnicity:
      (profileData as any)?.customer_profile?.ethnicity?.id || undefined,
    religion: (profileData as any)?.customer_profile?.religion?.id || undefined,
    interest: (() => {
      const interestData = (profileData as any)?.customer_profile?.interest;
      if (Array.isArray(interestData)) {
        return interestData.map((item: any) => item.id || item);
      } else if (interestData?.id) {
        return [interestData.id];
      } else if (interestData) {
        return [interestData];
      }
      return [];
    })(),
    city: (profileData as any)?.city || undefined,
    kids: (profileData as any)?.customer_profile?.kids || undefined,
    aboutMe: (profileData as any)?.customer_profile?.aboutMe || undefined,
    avatar: (profileData as any)?.avatar || undefined,
  };

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validateOnChange: false,
    validateOnBlur: true,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const payload = { ...values, avatar: preview };
        const result: any = await updateProfile(payload);
        if (result?.success) {
          toast.success(
            result?.message || t("myProfile.profileUpdatedSuccess")
          );
        }
      } finally {
        setSubmitting(false);
      }
    },
  });

  const handleSelectChange = React.useCallback(
    (field: string) => (option: any, isMulti?: boolean) => {
      if (isMulti === true) {
        formik.setFieldValue(field, option?.map((o: any) => o.value) || []);
        return;
      }
      formik.setFieldValue(field, option?.value || "");
    },
    [formik]
  );

  const handleBlurUncontrolled = React.useCallback(
    (field: string) =>
      (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        formik.setFieldValue(
          field,
          e.target.type === "number" ? Number(e.target.value) : e.target.value
        );
      },
    [formik]
  );

  const handleSelect = (value: any) => {
    setCurrentKey(value);
    setSearchParams({ tabKey: value });
  };

  return (
    <div className="my-profile">
      <Container fluid>
        <h3 className="fw-bold mb-4">{t("myProfile.title")}</h3>
        <Tab.Container
          defaultActiveKey={defaultTabKey}
          activeKey={currentKey}
          onSelect={handleSelect}
        >
          <Row>
            {/* Sidebar */}
            <Col xxl={3} lg={4} md={5}>
              <div className="my-profile-menus">
                <ProfilePictureSection
                  values={{ ...formik.values, avatar: preview }}
                  setFieldValue={formik.setFieldValue}
                  preview={preview}
                  handleImageChange={handleImageChange}
                />
                <Nav variant="pills" className="flex-column">
                  <Nav.Item>
                    <Nav.Link eventKey="viewProfile">
                      {t("myProfile.viewProfile")}
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="myPhotos">
                      {t("myProfile.myPhotos")}
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="changePassword">
                      {t("myProfile.changePassword")}
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="settings">
                      {t("myProfile.settings")}
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item className="logout-link" onClick={onLogout}>
                    <Nav.Link eventKey="logout">
                      {t("myProfile.logout")}
                    </Nav.Link>
                  </Nav.Item>
                </Nav>
              </div>
            </Col>
            {/* Content Area */}
            <Col xxl={9} lg={8} md={7}>
              <Tab.Content className="my-profile-content h-100">
                <Tab.Pane eventKey="viewProfile">
                  <APIProvider apiKey={GOOGLE_MAP_API_KEY}>
                    <Form noValidate onSubmit={formik.handleSubmit}>
                    <Accordion
                      className="d-flex flex-column"
                      defaultActiveKey={["0"]}
                    >
                      <MemoSection>
                        <BasicInfoSection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                          seekingForOptions={seekingForOptions}
                          genderOptions={genderOptions}
                        />
                      </MemoSection>
                      <MemoSection>
                        <AppearanceSection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                          handleSelectChange={handleSelectChange}
                          appearanceOptions={appearanceOptions}
                          hairColorOptions={hairColorOptions}
                          eyeColorOptions={eyeColorOptions}
                          bodyTypeOptions={bodyTypeOptions}
                          bestFeatureOptions={bestFeatureOptions}
                          bodyArtOptions={bodyArtOptions}
                          isMasterLoading={isMasterLoading}
                        />
                      </MemoSection>
                      <MemoSection>
                        <LifestyleSection
                          formik={formik}
                          handleSelectChange={handleSelectChange}
                          relationshipStatusOptions={relationshipStatusOptions}
                          starSignOptions={starSignOptions}
                          smokingHabitOptions={smokingHabitOptions}
                          drinkingHabitOptions={drinkingHabitOptions}
                          sexualOrientationOptions={sexualOrientationOptions}
                          isMasterLoading={isMasterLoading}
                        />
                      </MemoSection>
                      <MemoSection>
                        <PersonalAttributesSection
                          formik={formik}
                          handleSelectChange={handleSelectChange}
                          personalityOptions={personalityOptions}
                          ethnicityOptions={ethnicityOptions}
                          religionOptions={religionOptions}
                          interestsOptions={interestsOptions}
                          isMasterLoading={isMasterLoading}
                        />
                      </MemoSection>
                      <MemoSection>
                        <LocationSection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                        />
                      </MemoSection>
                      <MemoSection>
                        <FamilySection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                        />
                      </MemoSection>
                      <MemoSection>
                        <AboutMeSection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                        />
                      </MemoSection>
                    </Accordion>
                    <div className="d-flex gap-3 gap-md-4 mt-4 mt-md-5">
                      <Button type="submit" disabled={formik.isSubmitting}>
                        {t("myProfile.submit")}
                      </Button>
                      <Button className="bordered-btn" type="button">
                        {t("myProfile.cancel")}
                      </Button>
                    </div>
                  </Form>
                  </APIProvider>
                </Tab.Pane>
                <Tab.Pane eventKey="myPhotos">
                  <MyPhotos />
                </Tab.Pane>
                <Tab.Pane eventKey="changePassword">
                  <ChangePassword />
                </Tab.Pane>
                <Tab.Pane eventKey="settings">
                  <Settings />
                </Tab.Pane>
                <Tab.Pane eventKey="logout">
                  <h2>Logout</h2>
                  <p>You are now logged out.</p>
                </Tab.Pane>
              </Tab.Content>
            </Col>
          </Row>
        </Tab.Container>
      </Container>
    </div>
  );
};

export default MyProfile;

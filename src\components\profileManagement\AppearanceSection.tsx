import React from "react";
import { Accordion, Form } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import MemoizedSelect from "./MemoizedSelect";

interface AppearanceSectionProps {
  formik: any;
  handleBlurUncontrolled: (field: string) => (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (field: string) => (option: any) => void;
  appearanceOptions: Array<{ value: any; label: string }>;
  hairColorOptions: Array<{ value: any; label: string }>;
  eyeColorOptions: Array<{ value: any; label: string }>;
  bodyTypeOptions: Array<{ value: any; label: string }>;
  bestFeatureOptions: Array<{ value: any; label: string }>;
  bodyArtOptions: Array<{ value: any; label: string }>;
  isMasterLoading: boolean;
}

const AppearanceSection: React.FC<AppearanceSectionProps> = ({
  formik,
  handleBlurUncontrolled,
  handleSelectChange,
  appearanceOptions,
  hairColorOptions,
  eyeColorOptions,
  bodyTypeOptions,
  bestFeatureOptions,
  bodyArtOptions,
  isMasterLoading,
}) => {
  const { t } = useTranslation();

  return (
    <Accordion.Item eventKey="1">
      <Accordion.Header>{t("profileManagement.sections.appearance")}</Accordion.Header>
      <Accordion.Body>
        <div className="form-input-group d-flex flex-wrap">
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.appearance")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={appearanceOptions}
              placeholder={isMasterLoading ? t("common.loading") : t("profileManagement.placeholders.appearance")}
              classNamePrefix="select"
              name="appearance"
              value={formik.values.appearance}
              onChange={handleSelectChange("appearance")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.hairColor")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={hairColorOptions}
              placeholder={isMasterLoading ? t("common.loading") : t("profileManagement.placeholders.hairColor")}
              classNamePrefix="select"
              name="hairColor"
              value={formik.values.hairColor}
              onChange={handleSelectChange("hairColor")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.eyeColor")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={eyeColorOptions}
              placeholder={isMasterLoading ? t("common.loading") : t("profileManagement.placeholders.eyeColor")}
              classNamePrefix="select"
              name="eyeColor"
              value={formik.values.eyeColor}
              onChange={handleSelectChange("eyeColor")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.bodyType")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={bodyTypeOptions}
              placeholder={isMasterLoading ? t("common.loading") : t("profileManagement.placeholders.bodyType")}
              classNamePrefix="select"
              name="bodyType"
              value={formik.values.bodyType}
              onChange={handleSelectChange("bodyType")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.bestFeature")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={bestFeatureOptions}
              placeholder={isMasterLoading ? t("common.loading") : t("profileManagement.placeholders.bestFeature")}
              classNamePrefix="select"
              name="bestFeature"
              value={formik.values.bestFeature}
              onChange={handleSelectChange("bestFeature")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.bodyArt")}</Form.Label>
            <MemoizedSelect
              isSearchable
              options={bodyArtOptions}
              placeholder={isMasterLoading ? t("common.loading") : t("profileManagement.placeholders.bodyArt")}
              classNamePrefix="select"
              name="bodyArt"
              value={formik.values.bodyArt}
              onChange={handleSelectChange("bodyArt")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.height")}</Form.Label>
            <Form.Control
              type="number"
              placeholder={t("profileManagement.placeholders.height")}
              name="height"
              defaultValue={formik.values.height}
              onBlur={handleBlurUncontrolled("height")}
            />
          </Form.Group>
          <Form.Group className="form-input">
            <Form.Label>{t("profileManagement.fields.weight")}</Form.Label>
            <Form.Control
              type="number"
              placeholder={t("profileManagement.placeholders.weight")}
              name="weight"
              defaultValue={formik.values.weight}
              onBlur={handleBlurUncontrolled("weight")}
            />
          </Form.Group>
        </div>
      </Accordion.Body>
    </Accordion.Item>
  );
};

export default AppearanceSection;